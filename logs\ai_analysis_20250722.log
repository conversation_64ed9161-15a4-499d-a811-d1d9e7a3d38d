2025-07-22 13:23:12 - AIAnalysis - INFO - Logging system initialized
2025-07-22 13:23:12 - AIAnalysis - INFO - Log level: INFO
2025-07-22 13:23:12 - AIAnalysis - INFO - Console logging: True
2025-07-22 13:23:12 - AIAnalysis - INFO - File logging: True
2025-07-22 13:23:12 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-22 13:23:12 - AIAnalysis - ERROR - Failed to initialize application: OverflowHandler.__init__() missing 1 required positional argument: 'parent_widget'
2025-07-22 13:29:05 - AIAnalysis - INFO - Logging system initialized
2025-07-22 13:29:05 - AIAnalysis - INFO - Log level: INFO
2025-07-22 13:29:05 - AIAnalysis - INFO - Console logging: True
2025-07-22 13:29:05 - AIAnalysis - INFO - File logging: True
2025-07-22 13:29:05 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-22 13:29:06 - AIAnalysis - ERROR - Failed to initialize application: 'ContentManager' object has no attribute 'content_panels'
2025-07-22 13:29:36 - AIAnalysis - INFO - Logging system initialized
2025-07-22 13:29:36 - AIAnalysis - INFO - Log level: INFO
2025-07-22 13:29:36 - AIAnalysis - INFO - Console logging: True
2025-07-22 13:29:36 - AIAnalysis - INFO - File logging: True
2025-07-22 13:29:36 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-22 13:29:37 - AIAnalysis - ERROR - Failed to initialize application: 'ContentManager' object has no attribute 'content_panels'
2025-07-22 13:35:08 - AIAnalysis - INFO - Logging system initialized
2025-07-22 13:35:08 - AIAnalysis - INFO - Log level: INFO
2025-07-22 13:35:08 - AIAnalysis - INFO - Console logging: True
2025-07-22 13:35:08 - AIAnalysis - INFO - File logging: True
2025-07-22 13:35:08 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-22 13:35:09 - AIAnalysis - INFO - No scrollable frames found, but performance system is ready
2025-07-22 13:35:09 - AIAnalysis - INFO - Application initialized successfully
2025-07-22 13:35:09 - AIAnalysis - INFO - Starting application main loop
2025-07-22 13:36:40 - AIAnalysis - INFO - Application closing
2025-07-22 13:36:40 - AIAnalysis - INFO - Application cleanup completed
2025-07-22 23:03:49 - AIAnalysis - INFO - Logging system initialized
2025-07-22 23:03:49 - AIAnalysis - INFO - Log level: INFO
2025-07-22 23:03:49 - AIAnalysis - INFO - Console logging: True
2025-07-22 23:03:49 - AIAnalysis - INFO - File logging: True
2025-07-22 23:03:49 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-22 23:03:50 - AIAnalysis.OllamaManager - INFO - Initializing Ollama Manager
2025-07-22 23:03:52 - AIAnalysis.OllamaManager - INFO - Refreshed models cache: 14 models available
2025-07-22 23:03:52 - AIAnalysis.OllamaClient - INFO - Using enhanced Ollama manager
2025-07-22 23:03:52 - AIAnalysis.OllamaManager - INFO - Initializing Ollama Manager
2025-07-22 23:03:54 - AIAnalysis.OllamaManager - INFO - Refreshed models cache: 14 models available
2025-07-22 23:03:54 - AIAnalysis.EnhancedOllamaClient - INFO - Using enhanced Ollama manager
2025-07-22 23:03:55 - AIAnalysis - INFO - No scrollable frames found, but performance system is ready
2025-07-22 23:03:55 - AIAnalysis - INFO - Application initialized successfully
2025-07-22 23:03:55 - AIAnalysis - INFO - Starting application main loop
2025-07-22 23:05:31 - AIAnalysis - INFO - Application cleanup completed
2025-07-22 23:14:41 - AIAnalysis - INFO - Logging system initialized
2025-07-22 23:14:41 - AIAnalysis - INFO - Log level: INFO
2025-07-22 23:14:41 - AIAnalysis - INFO - Console logging: True
2025-07-22 23:14:41 - AIAnalysis - INFO - File logging: True
2025-07-22 23:14:41 - AIAnalysis - INFO - Log directory: F:\AI App Builds\Deep_Research_Tool_GUI\logs
2025-07-22 23:14:42 - AIAnalysis.OllamaManager - INFO - Initializing Ollama Manager
2025-07-22 23:14:44 - AIAnalysis.OllamaManager - INFO - Refreshed models cache: 14 models available
2025-07-22 23:14:44 - AIAnalysis.OllamaClient - INFO - Using enhanced Ollama manager
2025-07-22 23:14:44 - AIAnalysis.OllamaManager - INFO - Initializing Ollama Manager
2025-07-22 23:14:46 - AIAnalysis.OllamaManager - INFO - Refreshed models cache: 14 models available
2025-07-22 23:14:46 - AIAnalysis.EnhancedOllamaClient - INFO - Using enhanced Ollama manager
2025-07-22 23:14:46 - AIAnalysis.TopicListPanel - WARNING - Invalid topic structure in data\topics\Money_d1075000.json
2025-07-22 23:14:47 - AIAnalysis - INFO - No scrollable frames found, but performance system is ready
2025-07-22 23:14:47 - AIAnalysis - INFO - Application initialized successfully
2025-07-22 23:14:47 - AIAnalysis - INFO - Starting application main loop
2025-07-22 23:16:33 - AIAnalysis - INFO - Application closing
2025-07-22 23:16:33 - AIAnalysis - INFO - Application cleanup completed
