"""
Enhanced Main Window with Responsive Layout for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any
import time

from utils.logging_setup import LoggerMixin
from utils.config import Config
from utils.helpers import show_error, show_info, confirm_action
from utils.theme import <PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any
from analysis.ollama_client import OllamaClient
from analysis.enhanced_ollama_client import EnhancedOllamaClient
from analysis.advanced_analyzers import analysis_registry
from gui.topic_input import TopicInputPanel
from gui.topic_list import TopicListPanel
from gui.analysis_panel import AnalysisPanel
from gui.results_viewer import ResultsViewer
from gui.settings_dialog import SettingsDialog
from gui.progress_dialog import AnalysisProgressManager
from data.project_manager import ProjectManager

# Import new responsive components
from gui.responsive_layout import ResponsiveLayoutManager, AdaptivePanedWindow
from gui.enhanced_scrolling import EnhancedScrollableFrame
from gui.content_management import ContentManager


class EnhancedMainWindow(LoggerMixin):
    """Enhanced main application window with responsive layout"""
    
    def __init__(self, root: tk.Tk, config: Config):
        self.root = root
        self.config = config
        self.ollama_client = OllamaClient(config)
        self.enhanced_client = EnhancedOllamaClient(config)
        self.project_manager = ProjectManager(config)
        self.progress_manager = AnalysisProgressManager(root)

        # Initialize responsive layout manager
        self.layout_manager = ResponsiveLayoutManager(root)
        self.content_manager = ContentManager()

        # Initialize theme manager
        self.theme = ThemeManager(root)

        # Initialize components
        self.topic_input = None
        self.topic_list = None
        self.analysis_panel = None
        self.results_viewer = None
        
        # Responsive components
        self.main_paned = None
        self.right_paned = None
        self.left_scrollable = None
        self.analysis_scrollable = None
        self.results_scrollable = None

        # State variables
        self.current_analysis = None
        self.analysis_running = False
        self.current_project_id = None

        self.setup_ui()
        self.setup_menu()
        self.check_ollama_connection()
        
        # Apply initial layout
        self.layout_manager.apply_layout(force_update=True)
    
    def setup_ui(self):
        """Set up the enhanced responsive user interface"""
        # Create main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=3, pady=3)

        # Add toolbar
        self.setup_toolbar(main_frame)

        # Create adaptive paned window for resizable panels
        self.main_paned = AdaptivePanedWindow(
            main_frame, 
            self.layout_manager,
            orient=tk.HORIZONTAL
        )
        self.main_paned.pack(fill=tk.BOTH, expand=True, pady=(3, 0))

        # Left panel - Topic List and Topic Input with enhanced scrolling
        self.left_scrollable = EnhancedScrollableFrame(self.main_paned)
        left_main_frame = ttk.Frame(self.left_scrollable.get_frame())
        left_main_frame.pack(fill=tk.BOTH, expand=True)

        # Create vertical paned window for topic list and topic input
        left_paned = AdaptivePanedWindow(
            left_main_frame,
            self.layout_manager,
            orient=tk.VERTICAL
        )
        left_paned.pack(fill=tk.BOTH, expand=True)

        # Topic List panel (top)
        topic_list_frame = ttk.LabelFrame(
            left_paned,
            text="📚 Topic Library",
            padding=3
        )
        self.topic_list = TopicListPanel(topic_list_frame, self.config, self.theme, self.on_topic_selected)
        left_paned.add_panel(topic_list_frame, 'topic_list')

        # Topic Input panel (bottom)
        topic_input_frame = ttk.LabelFrame(
            left_paned,
            text="📝 Topic Input",
            padding=3
        )
        self.topic_input = TopicInputPanel(topic_input_frame, self.config, self.theme)
        left_paned.add_panel(topic_input_frame, 'topic_input')

        self.main_paned.add_panel(self.left_scrollable, 'left_panel')

        # Right panel container with adaptive behavior
        right_container = ttk.Frame(self.main_paned)
        self.main_paned.add_panel(right_container, 'right_container', weight=2)

        # Right panel - Analysis Control (top) and Results (bottom)
        self.right_paned = AdaptivePanedWindow(
            right_container,
            self.layout_manager,
            orient=tk.VERTICAL
        )
        self.right_paned.pack(fill=tk.BOTH, expand=True)

        # Analysis Control panel with enhanced scrolling
        self.analysis_scrollable = EnhancedScrollableFrame(self.right_paned)
        analysis_frame = ttk.LabelFrame(
            self.analysis_scrollable.get_frame(),
            text="⚙️ Analysis Control",
            padding=5
        )
        analysis_frame.pack(fill=tk.BOTH, expand=True)
        self.analysis_panel = AnalysisPanel(analysis_frame, self.config, self.on_start_analysis, self.theme)
        self.right_paned.add_panel(self.analysis_scrollable, 'analysis_panel')

        # Results panel with enhanced scrolling and content management
        self.results_scrollable = EnhancedScrollableFrame(self.right_paned)
        results_frame = ttk.LabelFrame(
            self.results_scrollable.get_frame(),
            text="📊 Results",
            padding=3
        )
        results_frame.pack(fill=tk.BOTH, expand=True)
        self.results_viewer = EnhancedResultsViewer(results_frame, self.config, self.theme, self.content_manager)
        self.right_paned.add_panel(self.results_scrollable, 'results_viewer', weight=3)

        # Register content panels with content manager
        self.content_manager.register_panel('results', self.results_viewer)
        self.content_manager.register_panel('analysis', self.analysis_panel)

        # Status bar with responsive layout
        self.setup_status_bar()

    def on_topic_selected(self, topic_data: Dict[str, Any]):
        """Handle topic selection from the topic library"""
        try:
            # Load the selected topic into the topic input panel
            self.topic_input.set_topic_data(topic_data)

            # Update status
            self.update_status(f"Loaded topic: {topic_data.get('title', 'Unknown')}")

            # Show success message
            show_info("Topic Loaded", f"Topic '{topic_data.get('title', 'Unknown')}' loaded successfully.")

        except Exception as e:
            self.logger.error(f"Failed to load selected topic: {e}")
            show_error("Load Error", f"Failed to load selected topic: {str(e)}")

    def setup_toolbar(self, parent):
        """Set up the responsive application toolbar"""
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 3))

        # Left side - Primary actions with compact layout
        left_toolbar = ttk.Frame(toolbar_frame)
        left_toolbar.pack(side=tk.LEFT)

        # Quick action buttons with adaptive widths
        self.toolbar_buttons = {
            'new': ttk.Button(left_toolbar, text="🆕", command=self.new_analysis),
            'open': ttk.Button(left_toolbar, text="📂", command=self.open_analysis),
            'save': ttk.Button(left_toolbar, text="💾", command=self.save_analysis),
            'start': ttk.Button(left_toolbar, text="▶️", command=self.start_analysis),
            'stop': ttk.Button(left_toolbar, text="⏹️", command=self.stop_analysis)
        }

        for i, (name, button) in enumerate(self.toolbar_buttons.items()):
            button.pack(side=tk.LEFT, padx=(0 if i == 0 else 2, 0))

        # Separator
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(
            side=tk.LEFT, fill=tk.Y, padx=5
        )

        # Right side - Settings and help
        right_toolbar = ttk.Frame(toolbar_frame)
        right_toolbar.pack(side=tk.RIGHT)

        ttk.Button(right_toolbar, text="⚙️", command=self.show_settings).pack(side=tk.RIGHT, padx=(2, 0))
        ttk.Button(right_toolbar, text="❓", command=self.show_user_guide).pack(side=tk.RIGHT, padx=(2, 0))

        # Register toolbar for responsive updates
        self.layout_manager.register_layout_callback('toolbar', self.update_toolbar_layout)
        
        # Apply initial layout to set correct button sizes
        self.root.after_idle(lambda: self.layout_manager.apply_layout(force_update=True))

    def update_toolbar_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update toolbar layout based on screen size"""
        if category in ['small']:
            # Keep icon-only buttons for small screens
            button_configs = {
                'new': {"text": "🆕", "width": 3},
                'open': {"text": "📂", "width": 3},
                'save': {"text": "💾", "width": 3},
                'start': {"text": "▶️", "width": 3},
                'stop': {"text": "⏹️", "width": 3}
            }
        elif category in ['medium']:
            # Add minimal text for medium screens
            button_configs = {
                'new': {"text": "🆕 New", "width": 8},
                'open': {"text": "📂 Open", "width": 9},
                'save': {"text": "💾 Save", "width": 9},
                'start': {"text": "▶️ Start", "width": 10},
                'stop': {"text": "⏹️ Stop", "width": 9}
            }
        else:
            # Full text for large screens
            button_configs = {
                'new': {"text": "🆕 New Analysis", "width": 16},
                'open': {"text": "📂 Open Project", "width": 16},
                'save': {"text": "💾 Save Project", "width": 16},
                'start': {"text": "▶️ Start Analysis", "width": 18},
                'stop': {"text": "⏹️ Stop Analysis", "width": 17}
            }

        # Update button texts and widths
        for name, button in self.toolbar_buttons.items():
            config = button_configs.get(name, {})
            if config:
                try:
                    button.configure(text=config["text"], width=config["width"])
                except tk.TclError:
                    pass

    def setup_status_bar(self):
        """Set up responsive status bar"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=3, pady=(0, 3))

        # Left side - Status information
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(self.status_frame, textvariable=self.status_var)
        self.status_label.pack(side=tk.LEFT)

        # Right side - Progress and controls
        self.progress_frame = ttk.Frame(self.status_frame)
        self.progress_frame.pack(side=tk.RIGHT)

        self.progress_var = tk.StringVar()
        self.progress_label = ttk.Label(self.progress_frame, textvariable=self.progress_var, width=20)
        self.progress_label.pack(side=tk.RIGHT, padx=(5, 0))

        # Register status bar for responsive updates
        self.layout_manager.register_layout_callback('status', self.update_status_layout)

    def update_status_layout(self, category: str, weights: Dict[str, int], min_widths: Dict[str, int]):
        """Update status bar layout based on screen size"""
        if category == 'small':
            # Compact status for small screens
            self.progress_label.configure(width=15)
        else:
            # Full status for larger screens
            self.progress_label.configure(width=25)

    # Implement required methods from original MainWindow
    def setup_menu(self):
        """Set up the application menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Analysis", command=self.new_analysis)
        file_menu.add_command(label="Open Project", command=self.open_project)
        file_menu.add_command(label="Save Project", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Settings", command=self.open_settings)
        tools_menu.add_command(label="Refresh Ollama Connection", command=self.check_ollama_connection)
        tools_menu.add_separator()
        tools_menu.add_command(label="Clear Cache", command=self.clear_cache)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)

    def check_ollama_connection(self):
        """Check Ollama connection status and load available models"""
        try:
            # Test connection
            connection_result = self.ollama_client.test_connection()

            if connection_result.get("success", False):
                # Connection successful, load models
                models = self.ollama_client.list_models()

                if models:
                    # Update analysis panel with available models
                    if self.analysis_panel:
                        self.analysis_panel.update_models(models)

                    self.logger.info(f"Ollama connection successful. Found {len(models)} models.")
                    self.update_status(f"✅ Connected to Ollama - {len(models)} models available")
                else:
                    self.logger.warning("Ollama connected but no models found")
                    self.update_status("⚠️ Ollama connected but no models available")
            else:
                error_msg = connection_result.get("error", "Unknown connection error")
                self.logger.error(f"Ollama connection failed: {error_msg}")
                self.update_status("❌ Ollama connection failed")

                # Show error dialog
                show_error("Ollama Connection Error",
                          f"Failed to connect to Ollama:\n{error_msg}\n\n"
                          "Please ensure Ollama is running and accessible.")

        except Exception as e:
            self.logger.error(f"Error checking Ollama connection: {e}")
            self.update_status("❌ Error checking Ollama connection")
            show_error("Connection Error", f"Error checking Ollama connection:\n{str(e)}")

    def update_status(self, message: str):
        """Update status bar message"""
        # For now, just log the status. In a full implementation,
        # this would update a status bar widget
        self.logger.info(f"Status: {message}")

    def new_analysis(self):
        """Create new analysis"""
        if self.topic_input:
            self.topic_input.clear()
        if self.results_viewer:
            self.results_viewer.clear()
        self.current_analysis = None
        self.current_project_id = None
        self.update_status("Ready for new analysis")

    def open_project(self):
        """Open an existing project"""
        # This would open a project selection dialog
        show_info("Open Project", "Project opening functionality will be implemented here.")

    def save_project(self):
        """Save current project"""
        # This would save the current analysis as a project
        show_info("Save Project", "Project saving functionality will be implemented here.")

    def open_settings(self):
        """Open settings dialog"""
        try:
            dialog = SettingsDialog(self.root, self.config)
            if dialog.result == "ok":
                # Refresh connection after settings change
                self.check_ollama_connection()
        except Exception as e:
            self.logger.error(f"Error opening settings: {e}")
            show_error("Settings Error", f"Failed to open settings: {str(e)}")

    def clear_cache(self):
        """Clear application cache"""
        try:
            self.ollama_client.clear_cache()
            show_info("Cache Cleared", "Application cache has been cleared.")
        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")
            show_error("Cache Error", f"Failed to clear cache: {str(e)}")

    def show_about(self):
        """Show about dialog"""
        about_text = """AI Analysis Program - Enhanced Edition

A comprehensive tool for topical AI analysis using Ollama API.

Features:
• Multiple analysis types (Iterative, Recursive, Comparative, SWOT, Temporal)
• Interactive mind map generation
• Project management and history
• Responsive user interface
• Performance optimizations

Version: 2.0
Built with Python and Tkinter"""

        show_info("About AI Analysis Program", about_text)

    def start_analysis(self):
        """Start analysis process"""
        # Implementation from original main_window.py
        pass

    def stop_analysis(self):
        """Stop analysis process"""
        self.analysis_running = False
        self.update_status("Analysis stopped")

    def _run_iterative_analysis(self, topic_data, analysis_config):
        """Run iterative analysis"""
        try:
            results = {
                "type": "iterative",
                "topic": topic_data.get('title', ''),
                "sub_analyses": [],
                "connections": "",
                "metadata": {
                    "model": analysis_config.get('model'),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
            }

            # Analyze main topic
            main_analysis = self.ollama_client.analyze_topic(
                topic_data.get('title', ''),
                context=topic_data.get('description', ''),
                analysis_type="detailed"
            )

            if main_analysis:
                results["main_analysis"] = main_analysis

            # Analyze sub-topics
            sub_topics = topic_data.get('sub_topics', [])
            for sub_topic in sub_topics:
                if isinstance(sub_topic, dict):
                    title = sub_topic.get('title', '')
                    description = sub_topic.get('description', '')
                else:
                    title = str(sub_topic)
                    description = ''

                if title:
                    analysis = self.ollama_client.analyze_topic(title, context=description)
                    if analysis:
                        results["sub_analyses"].append({
                            "topic": title,
                            "description": description,
                            "analysis": analysis
                        })

            # Find connections
            if len(results["sub_analyses"]) > 1:
                topic_titles = [sa["topic"] for sa in results["sub_analyses"]]
                connections = self.ollama_client.find_connections(topic_titles)
                if connections:
                    results["connections"] = connections

            return results

        except Exception as e:
            self.logger.error(f"Iterative analysis error: {e}")
            return {"error": str(e)}

    def _run_recursive_analysis(self, topic_data, analysis_config):
        """Run recursive analysis"""
        try:
            max_depth = analysis_config.get('max_depth', 3)

            results = {
                "type": "recursive",
                "topic": topic_data.get('title', ''),
                "analysis_tree": {},
                "metadata": {
                    "model": analysis_config.get('model'),
                    "max_depth": max_depth,
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }
            }

            # Start recursive analysis
            results["analysis_tree"] = self._recursive_analyze(
                topic_data.get('title', ''),
                topic_data.get('description', ''),
                0, max_depth, analysis_config
            )

            return results

        except Exception as e:
            self.logger.error(f"Recursive analysis error: {e}")
            return {"error": str(e)}

    def _recursive_analyze(self, topic, context, current_depth, max_depth, config):
        """Recursively analyze a topic"""
        if current_depth >= max_depth:
            return {"topic": topic, "analysis": "Max depth reached", "subtopics": []}

        # Analyze current topic
        analysis = self.ollama_client.analyze_topic(topic, context=context, analysis_type="detailed")

        # Generate subtopics
        subtopic_prompt = f"Based on the topic '{topic}', suggest 3-4 related subtopics for deeper analysis. Return only the subtopic names, one per line."
        subtopics_text = self.ollama_client.generate(subtopic_prompt)

        subtopics = []
        if subtopics_text:
            subtopic_lines = [line.strip() for line in subtopics_text.split('\n') if line.strip()]
            for subtopic in subtopic_lines[:4]:  # Limit to 4 subtopics
                subtopic_analysis = self._recursive_analyze(subtopic, "", current_depth + 1, max_depth, config)
                subtopics.append(subtopic_analysis)

        return {
            "topic": topic,
            "analysis": analysis or "Analysis failed",
            "subtopics": subtopics,
            "depth": current_depth
        }

    def _run_advanced_analysis(self, topic_data, analysis_config):
        """Run advanced analysis (comparative, SWOT, temporal)"""
        try:
            analysis_type = analysis_config.get('type')

            # Use the advanced analyzers registry
            results = analysis_registry.analyze(
                analysis_type,
                topic_data,
                self.ollama_client
            )

            # Add metadata
            if isinstance(results, dict):
                results["metadata"] = {
                    "model": analysis_config.get('model'),
                    "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                }

            return results

        except Exception as e:
            self.logger.error(f"Advanced analysis error: {e}")
            return {"error": str(e)}

    def _display_analysis_results(self, results):
        """Display analysis results in the UI"""
        try:
            if self.results_viewer:
                self.results_viewer.display_results(results)

            # Store current analysis
            self.current_analysis = results

            # Update status
            if "error" in results:
                self.update_status(f"❌ Analysis failed: {results['error']}")
            else:
                analysis_type = results.get('type', 'unknown')
                self.update_status(f"✅ {analysis_type.title()} analysis completed")

        except Exception as e:
            self.logger.error(f"Error displaying results: {e}")
            show_error("Display Error", f"Failed to display results: {str(e)}")

    def on_start_analysis(self, analysis_config):
        """Handle analysis start"""
        try:
            # Get topic data
            if not self.topic_input:
                show_error("No Topic", "Please enter a topic for analysis.")
                return

            topic_data = self.topic_input.get_topic_data()
            if not topic_data or not topic_data.get('title'):
                show_error("No Topic", "Please enter a topic title for analysis.")
                return

            # Validate model selection
            selected_model = analysis_config.get('model')
            if not selected_model:
                show_error("No Model", "Please select an AI model for analysis.")
                return

            # Set analysis running state
            self.analysis_running = True
            self.update_status(f"Starting {analysis_config.get('type', 'general')} analysis...")

            # Start analysis in background thread
            import threading
            analysis_thread = threading.Thread(
                target=self._run_analysis_thread,
                args=(topic_data, analysis_config),
                daemon=True
            )
            analysis_thread.start()

        except Exception as e:
            self.logger.error(f"Error starting analysis: {e}")
            show_error("Analysis Error", f"Failed to start analysis: {str(e)}")
            self.analysis_running = False

    def _run_analysis_thread(self, topic_data, analysis_config):
        """Run analysis in background thread"""
        try:
            analysis_type = analysis_config.get('type', 'iterative')

            # Update UI
            self.root.after(0, lambda: self.update_status(f"Running {analysis_type} analysis..."))

            # Perform analysis based on type
            if analysis_type == "iterative":
                results = self._run_iterative_analysis(topic_data, analysis_config)
            elif analysis_type == "recursive":
                results = self._run_recursive_analysis(topic_data, analysis_config)
            elif analysis_type in ["comparative", "swot", "temporal"]:
                results = self._run_advanced_analysis(topic_data, analysis_config)
            else:
                results = {"error": f"Unknown analysis type: {analysis_type}"}

            # Update UI with results
            self.root.after(0, lambda: self._display_analysis_results(results))

        except Exception as e:
            self.logger.error(f"Analysis thread error: {e}")
            self.root.after(0, lambda: show_error("Analysis Error", f"Analysis failed: {str(e)}"))
        finally:
            self.analysis_running = False
            self.root.after(0, lambda: self.update_status("Analysis completed"))

    def on_closing(self):
        """Handle application closing"""
        if self.analysis_running:
            if not confirm_action("Analysis Running",
                                "An analysis is currently running. Are you sure you want to exit?"):
                return False

        # Save any pending work
        try:
            if self.current_analysis:
                # Auto-save current work if needed
                pass
        except Exception as e:
            self.logger.error(f"Error during closing: {e}")

        return True

    def confirm_close(self):
        """Confirm application close"""
        return self.on_closing()

    def cleanup(self):
        """Cleanup resources"""
        try:
            # Stop any running analyses
            if self.analysis_running:
                self.stop_analysis()

            # Cleanup components
            if hasattr(self, 'progress_manager'):
                self.progress_manager.cleanup()

            self.logger.info("Enhanced main window cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")

    def show_user_guide(self):
        """Show user guide"""
        # Implementation from original main_window.py
        pass

    def confirm_close(self) -> bool:
        """Confirm application close"""
        # Implementation from original main_window.py
        return True

    def cleanup(self):
        """Cleanup resources"""
        # Implementation from original main_window.py
        pass


class EnhancedResultsViewer(ResultsViewer):
    """Enhanced results viewer with content management"""
    
    def __init__(self, parent: tk.Widget, config: Config, theme=None, content_manager=None):
        self.content_manager = content_manager
        super().__init__(parent, config, theme)
        
    def display_results(self, results: Dict[str, Any]):
        """Display results with content management"""
        if self.content_manager:
            # Add results to content manager for overflow handling
            self.content_manager.add_content(
                'results', 
                results, 
                priority='high',
                content_type='analysis_results'
            )
        
        # Call parent implementation
        super().display_results(results)


# Maintain backward compatibility
class MainWindow(EnhancedMainWindow):
    """Backward compatible main window class"""
    pass
