#!/usr/bin/env python3
"""
Test script to validate all the fixes made to the AI Analysis Program
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_codebase_cleanup():
    """Test that redundant files have been removed"""
    print("Testing codebase cleanup...")
    
    # Files that should be removed
    removed_files = [
        "AI_Analysis_Program_Plan.md",
        "gui/main_window.py",
        "apply_gui_improvements.py",
        "demo_eta_gui.py"
    ]
    
    missing_count = 0
    for file_path in removed_files:
        if not Path(file_path).exists():
            missing_count += 1
            print(f"✓ {file_path} successfully removed")
        else:
            print(f"✗ {file_path} still exists")
    
    print(f"✓ Cleanup test: {missing_count}/{len(removed_files)} files properly removed")
    return missing_count == len(removed_files)

def test_ai_model_integration():
    """Test AI model integration"""
    print("\nTesting AI model integration...")
    
    try:
        from utils.config import Config
        from analysis.ollama_client import OllamaClient
        
        config = Config()
        client = OllamaClient(config)
        
        # Test connection
        connection_result = client.test_connection()
        if connection_result.get("success", False):
            print("✓ Ollama connection successful")
            
            # Test model listing
            models = client.list_models()
            if models:
                print(f"✓ Found {len(models)} available models")
                print(f"  Default model: {config.get('ollama.default_model')}")
                
                # Test if default model is available
                model_names = [m.get("name", "") for m in models]
                default_model = config.get('ollama.default_model')
                if default_model in model_names:
                    print(f"✓ Default model '{default_model}' is available")
                else:
                    print(f"⚠ Default model '{default_model}' not found, but other models available")
                
                return True
            else:
                print("✗ No models found")
                return False
        else:
            print(f"✗ Ollama connection failed: {connection_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"✗ AI model integration test failed: {e}")
        return False

def test_button_utilities():
    """Test button utilities and responsive sizing"""
    print("\nTesting button utilities...")
    
    try:
        from gui.button_utils import create_responsive_button, ButtonSizeManager
        
        # Test button size manager
        manager = ButtonSizeManager()
        
        # Test width calculation
        width = manager.calculate_button_width("🚀 Quick")
        print(f"✓ Button width calculation works: '{width}' chars for '🚀 Quick'")
        
        # Test preset configurations
        presets = manager.get_preset_widths()
        if presets:
            print(f"✓ Preset widths available: {list(presets.keys())}")
        
        print("✓ Button utilities working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Button utilities test failed: {e}")
        return False

def test_topic_library():
    """Test topic library functionality"""
    print("\nTesting topic library...")
    
    try:
        # Check if topic library directory exists
        topics_dir = Path("data/topics")
        if topics_dir.exists():
            print(f"✓ Topic library directory exists")
            
            # Count existing topics
            topic_files = list(topics_dir.glob("*.json"))
            print(f"✓ Found {len(topic_files)} existing topics")
            
            # Test topic library loading
            from gui.topic_list import TopicListPanel
            from utils.config import Config
            
            config = Config()
            # We can't fully test the GUI without a display, but we can test the structure
            print("✓ TopicListPanel class available")
            
            return True
        else:
            print("⚠ Topic library directory doesn't exist yet (will be created on first use)")
            return True
            
    except Exception as e:
        print(f"✗ Topic library test failed: {e}")
        return False

def test_enhanced_main_window():
    """Test enhanced main window functionality"""
    print("\nTesting enhanced main window...")
    
    try:
        from gui.enhanced_main_window import EnhancedMainWindow
        from utils.config import Config
        
        config = Config()
        print("✓ EnhancedMainWindow imports successfully")
        
        # Test that the class has the required methods
        required_methods = [
            'check_ollama_connection',
            'on_start_analysis',
            'on_topic_selected',
            'setup_menu'
        ]
        
        for method in required_methods:
            if hasattr(EnhancedMainWindow, method):
                print(f"✓ Method '{method}' available")
            else:
                print(f"✗ Method '{method}' missing")
                return False
        
        print("✓ Enhanced main window structure correct")
        return True
        
    except Exception as e:
        print(f"✗ Enhanced main window test failed: {e}")
        return False

def test_analysis_functionality():
    """Test analysis functionality"""
    print("\nTesting analysis functionality...")
    
    try:
        from analysis.advanced_analyzers import analysis_registry
        
        # Test analysis registry
        available_types = analysis_registry.list_available()
        if available_types:
            print(f"✓ Analysis types available: {len(available_types)} types")
        else:
            print("✗ No analysis types found")
            return False

        # Extract type names from the registry format
        type_names = []
        if isinstance(available_types, list) and available_types:
            if isinstance(available_types[0], dict):
                type_names = [t.get('name', '') for t in available_types]
            else:
                type_names = available_types

        # Test that required analysis types exist
        required_types = ['comparative', 'swot', 'temporal']
        for analysis_type in required_types:
            if analysis_type in type_names:
                print(f"✓ Analysis type '{analysis_type}' available")
            else:
                print(f"✗ Analysis type '{analysis_type}' missing")
                return False
        
        print("✓ Analysis functionality working correctly")
        return True
        
    except Exception as e:
        print(f"✗ Analysis functionality test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("AI Analysis Program - Fix Validation Tests")
    print("=" * 50)
    
    tests = [
        ("Codebase Cleanup", test_codebase_cleanup),
        ("AI Model Integration", test_ai_model_integration),
        ("Button Utilities", test_button_utilities),
        ("Topic Library", test_topic_library),
        ("Enhanced Main Window", test_enhanced_main_window),
        ("Analysis Functionality", test_analysis_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes validated successfully!")
        print("\nSummary of fixes:")
        print("1. ✅ Codebase cleaned up - removed redundant files")
        print("2. ✅ AI model integration fixed - Ollama connection working")
        print("3. ✅ Button visibility issues resolved - responsive sizing implemented")
        print("4. ✅ Topic library selection enhanced - multi-topic selection available")
        print("5. ✅ All components tested and validated")
        
        print("\nThe application is ready to use!")
        print("Run: python main.py")
        return 0
    else:
        print(f"❌ {total - passed} test(s) failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
