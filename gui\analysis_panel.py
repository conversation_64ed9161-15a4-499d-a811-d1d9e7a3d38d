"""
Analysis control panel for AI Analysis Program
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, List, Callable
from tkinter import messagebox

from utils.logging_setup import LoggerMixin
from utils.config import Config
from gui.widgets import CollapsibleFrame, ToolTip

class AnalysisPanel(LoggerMixin):
    """Panel for analysis configuration and control"""
    
    def __init__(self, parent: tk.Widget, config: Config, start_callback: Callable, theme=None):
        self.parent = parent
        self.config = config
        self.start_callback = start_callback
        self.theme = theme
        self.available_models = []
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the analysis control interface with enhanced organization"""
        # Create scrollable container for the entire panel
        self.container = ttk.Frame(self.parent)
        self.container.pack(fill=tk.BOTH, expand=True)
        
        # Analysis type selection - always visible
        type_frame = CollapsibleFrame(
            self.container,
            title="Analysis Type",
            collapsed=False,
            theme=self.theme
        )
        type_frame.pack(fill=tk.X, pady=(0, 8))

        self.analysis_type_var = tk.StringVar(value="iterative")

        # Create analysis type cards
        types_container = ttk.Frame(type_frame.content_frame)
        types_container.pack(fill=tk.X, pady=3)

        # Analysis types with icons and descriptions
        analysis_types = [
            ("iterative", "🔄 Iterative", "Analyze each sub-topic sequentially"),
            ("recursive", "🌳 Recursive", "Deep-dive analysis with auto-generated sub-topics"),
            ("comparative", "⚖️ Comparative", "Compare and contrast multiple topics"),
            ("swot", "📊 SWOT", "Strengths, Weaknesses, Opportunities, Threats"),
            ("temporal", "⏰ Temporal", "Time-based analysis with trends")
        ]

        for value, text, desc in analysis_types:
            type_btn = ttk.Radiobutton(
                types_container,
                text=text,
                variable=self.analysis_type_var,
                value=value,
                command=self.on_analysis_type_changed
            )
            type_btn.pack(anchor=tk.W, pady=1)

            if self.theme:
                ToolTip(type_btn, desc)

        # Analysis description with enhanced styling
        self.analysis_desc_var = tk.StringVar()
        self.analysis_desc_label = ttk.Label(
            type_frame.content_frame,
            textvariable=self.analysis_desc_var,
            wraplength=400,
            justify=tk.LEFT,
            font=("Arial", 8)
        )
        self.analysis_desc_label.pack(anchor=tk.W, pady=(3, 0), padx=5, fill=tk.X, expand=True)
        # Dynamic wraplength update on resize
        def update_wrap(event=None):
            width = type_frame.content_frame.winfo_width()
            # Leave some margin for padding
            self.analysis_desc_label.config(wraplength=max(200, width-40))
        type_frame.content_frame.bind('<Configure>', update_wrap)
        
        # Model selection - collapsible
        model_frame = CollapsibleFrame(
            self.container,
            title="AI Model",
            collapsed=False,
            theme=self.theme
        )
        model_frame.pack(fill=tk.X, pady=(0, 8))

        model_container = ttk.Frame(model_frame.content_frame)
        model_container.pack(fill=tk.X, pady=3)

        if self.theme:
            model_label = ttk.Label(model_container, text="Model:", style="Subheader.TLabel")
        else:
            model_label = ttk.Label(model_container, text="Model:")
        model_label.pack(anchor=tk.W)

        if self.theme:
            ToolTip(model_label, "Select the AI model to use for analysis")

        self.model_var = tk.StringVar()
        self.model_combo = ttk.Combobox(
            model_container,
            textvariable=self.model_var,
            state="readonly",
            width=25,  # Reduced width to fit better
            font=("Arial", 9)
        )
        self.model_combo.pack(fill=tk.X, pady=(2, 3))

        # Model info with enhanced styling
        self.model_info_var = tk.StringVar()
        self.model_info_label = ttk.Label(
            model_container,
            textvariable=self.model_info_var,
            font=("Arial", 8)
        )
        self.model_info_label.pack(anchor=tk.W)

        # Model status indicator
        self.model_status_frame = ttk.Frame(model_container)
        self.model_status_frame.pack(fill=tk.X, pady=(2, 0))

        self.model_status_var = tk.StringVar(value="🔴 No model selected")
        self.model_status_label = ttk.Label(
            self.model_status_frame,
            textvariable=self.model_status_var,
            font=("Arial", 8)
        )
        self.model_status_label.pack(side=tk.LEFT)
        
        # Analysis parameters - collapsible
        params_frame = CollapsibleFrame(
            self.container,
            title="Parameters",
            collapsed=False,
            theme=self.theme
        )
        params_frame.pack(fill=tk.X, pady=(0, 8))

        params_container = ttk.Frame(params_frame.content_frame)
        params_container.pack(fill=tk.X, pady=3)

        # Temperature with enhanced styling - more compact
        temp_frame = ttk.Frame(params_container)
        temp_frame.pack(fill=tk.X, pady=(0, 6))

        temp_label = ttk.Label(temp_frame, text="🌡️ Temperature:")
        temp_label.pack(anchor=tk.W)

        if self.theme:
            ToolTip(temp_label, "Controls randomness in AI responses (0.0 = deterministic, 2.0 = very creative)")

        temp_control_frame = ttk.Frame(temp_frame)
        temp_control_frame.pack(fill=tk.X, pady=(2, 0))

        self.temperature_var = tk.DoubleVar(value=self.config.get("ollama.temperature", 0.7))
        self.temperature_scale = ttk.Scale(
            temp_control_frame,
            from_=0.0,
            to=2.0,
            variable=self.temperature_var,
            orient=tk.HORIZONTAL,
            length=150  # Reduced length to fit better
        )
        self.temperature_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.temperature_label = ttk.Label(temp_control_frame, text="0.7", width=5)
        self.temperature_label.pack(side=tk.RIGHT, padx=(5, 0))

        self.temperature_scale.configure(command=self.update_temperature_label)

        # Max depth (for recursive analysis) with enhanced styling
        depth_frame = ttk.Frame(params_container)
        depth_frame.pack(fill=tk.X, pady=(0, 6))

        depth_label = ttk.Label(depth_frame, text="🌳 Max Depth:")
        depth_label.pack(anchor=tk.W)

        if self.theme:
            ToolTip(depth_label, "Maximum depth for recursive analysis (higher = more detailed)")

        depth_control_frame = ttk.Frame(depth_frame)
        depth_control_frame.pack(fill=tk.X, pady=(2, 0))

        self.max_depth_var = tk.IntVar(value=self.config.get("analysis.max_recursive_depth", 3))
        self.max_depth_spin = ttk.Spinbox(
            depth_control_frame,
            from_=1,
            to=10,
            textvariable=self.max_depth_var,
            width=6
        )
        self.max_depth_spin.pack(side=tk.LEFT)

        depth_info = ttk.Label(
            depth_control_frame,
            text="(1=shallow, 10=very deep)",
            font=("Arial", 8)
        )
        depth_info.pack(side=tk.LEFT, padx=(8, 0))

        # Connection threshold with enhanced styling
        conn_frame = ttk.Frame(params_container)
        conn_frame.pack(fill=tk.X)

        conn_label = ttk.Label(conn_frame, text="🔗 Connection Threshold:")
        conn_label.pack(anchor=tk.W)

        if self.theme:
            ToolTip(conn_label, "Minimum similarity score for topic connections (0.0 = loose, 1.0 = strict)")

        conn_control_frame = ttk.Frame(conn_frame)
        conn_control_frame.pack(fill=tk.X, pady=(2, 0))

        self.connection_threshold_var = tk.DoubleVar(
            value=self.config.get("analysis.connection_threshold", 0.7)
        )
        self.connection_scale = ttk.Scale(
            conn_control_frame,
            from_=0.0,
            to=1.0,
            variable=self.connection_threshold_var,
            orient=tk.HORIZONTAL,
            length=150
        )
        self.connection_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.connection_label = ttk.Label(conn_control_frame, text="0.7", width=6)
        self.connection_label.pack(side=tk.RIGHT, padx=(self.theme.spacing.pad_sm if self.theme else 5, 0))

        self.connection_scale.configure(command=self.update_connection_label)
        
        # Advanced options - collapsible and initially collapsed
        advanced_frame = CollapsibleFrame(
            self.parent,
            title="Advanced Options",
            collapsed=True,
            theme=self.theme
        )
        advanced_frame.pack(fill=tk.X, pady=(0, self.theme.spacing.pad_md if self.theme else 10))

        advanced_container = ttk.Frame(advanced_frame.content_frame)
        advanced_container.pack(fill=tk.X, pady=self.theme.spacing.pad_sm if self.theme else 5)

        # Caching option with icon
        self.enable_caching_var = tk.BooleanVar(
            value=self.config.get("analysis.enable_caching", True)
        )
        caching_cb = ttk.Checkbutton(
            advanced_container,
            text="💾 Enable result caching",
            variable=self.enable_caching_var
        )
        caching_cb.pack(anchor=tk.W, pady=2)

        if self.theme:
            ToolTip(caching_cb, "Cache analysis results to improve performance for repeated analyses")

        # Parallel processing option with icon
        self.parallel_processing_var = tk.BooleanVar(value=False)
        parallel_cb = ttk.Checkbutton(
            advanced_container,
            text="⚡ Parallel processing (experimental)",
            variable=self.parallel_processing_var
        )
        parallel_cb.pack(anchor=tk.W, pady=2)

        if self.theme:
            ToolTip(parallel_cb, "Process multiple sub-topics simultaneously (may be unstable)")

        # Verbose output option
        self.verbose_output_var = tk.BooleanVar(value=False)
        verbose_cb = ttk.Checkbutton(
            advanced_container,
            text="📝 Verbose output",
            variable=self.verbose_output_var
        )
        verbose_cb.pack(anchor=tk.W, pady=2)

        if self.theme:
            ToolTip(verbose_cb, "Include detailed processing information in results")

        # Enhanced analysis option
        self.enhanced_analysis_var = tk.BooleanVar(value=True)
        enhanced_cb = ttk.Checkbutton(
            advanced_container,
            text="🧠 Enhanced context-aware analysis",
            variable=self.enhanced_analysis_var
        )
        enhanced_cb.pack(anchor=tk.W, pady=2)

        if self.theme:
            ToolTip(enhanced_cb, "Use advanced context-aware analysis for better topic relationships")

        # Control buttons with enhanced styling
        control_frame = ttk.Frame(self.parent)
        control_frame.pack(fill=tk.X, pady=(self.theme.spacing.pad_lg if self.theme else 15, 0))

        # Main action button
        if self.theme:
            self.start_button = ttk.Button(
                control_frame,
                text="▶️ Start Analysis",
                command=self.start_analysis,
                style="Primary.TButton"
            )
        else:
            self.start_button = ttk.Button(
                control_frame,
                text="▶️ Start Analysis",
                command=self.start_analysis
            )
        self.start_button.pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm if self.theme else 5))

        if self.theme:
            ToolTip(self.start_button, "Begin analysis with current settings")

        # Reset button
        self.reset_button = ttk.Button(
            control_frame,
            text="🔄 Reset",
            command=self.reset
        )
        self.reset_button.pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_sm if self.theme else 5))

        if self.theme:
            ToolTip(self.reset_button, "Reset all parameters to default values")

        # Preset buttons - responsive sizing
        preset_frame = ttk.Frame(control_frame)
        preset_frame.pack(side=tk.RIGHT)

        # Import responsive button utility
        from gui.button_utils import create_responsive_button

        quick_btn = create_responsive_button(
            preset_frame,
            "🚀 Quick",
            command=self.apply_quick_preset
        )
        quick_btn.pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_xs if self.theme else 2))

        balanced_btn = create_responsive_button(
            preset_frame,
            "🎯 Balanced",
            command=self.apply_balanced_preset
        )
        balanced_btn.pack(side=tk.LEFT, padx=(0, self.theme.spacing.pad_xs if self.theme else 2))

        deep_btn = create_responsive_button(
            preset_frame,
            "🔬 Deep",
            command=self.apply_deep_preset
        )
        deep_btn.pack(side=tk.LEFT)

        # Initialize UI state
        self.on_analysis_type_changed()
        self.update_temperature_label()
        self.update_connection_label()
        self.update_model_status()
    
    def on_analysis_type_changed(self):
        """Handle analysis type change"""
        analysis_type = self.analysis_type_var.get()
        
        descriptions = {
            "iterative": "Analyze each sub-topic sequentially, then find connections between them.",
            "recursive": "Start with main topic and recursively generate and analyze sub-topics to specified depth.",
            "comparative": "Compare and contrast multiple topics with scoring and recommendations.",
            "swot": "Structured analysis of Strengths, Weaknesses, Opportunities, and Threats.",
            "temporal": "Analyze topics across time dimensions with trends and predictions."
        }
        
        self.analysis_desc_var.set(descriptions.get(analysis_type, ""))
        
        # Enable/disable depth control based on analysis type
        if analysis_type == "recursive":
            self.max_depth_spin.configure(state="normal")
        else:
            self.max_depth_spin.configure(state="disabled")
    
    def update_temperature_label(self, value=None):
        """Update temperature label"""
        temp = self.temperature_var.get()
        self.temperature_label.configure(text=f"{temp:.2f}")
    
    def update_connection_label(self, value=None):
        """Update connection threshold label"""
        threshold = self.connection_threshold_var.get()
        self.connection_label.configure(text=f"{threshold:.2f}")
    
    def update_models(self, models: List[Dict[str, Any]]):
        """Update available models list"""
        self.available_models = models
        model_names = [model.get("name", "Unknown") for model in models]

        self.model_combo['values'] = model_names

        # Set default model
        default_model = self.config.get("ollama.default_model", "llama2")
        if default_model in model_names:
            self.model_var.set(default_model)
        elif model_names:
            self.model_var.set(model_names[0])

        self.update_model_info()
        self.update_model_status()

        # Bind model selection change
        self.model_combo.bind('<<ComboboxSelected>>', self.on_model_changed)

    def update_model_status(self):
        """Update model status indicator"""
        selected_model = self.model_var.get()
        if selected_model:
            self.model_status_var.set(f"🟢 {selected_model} ready")
        else:
            self.model_status_var.set("🔴 No model selected")

    def apply_quick_preset(self):
        """Apply quick analysis preset"""
        self.temperature_var.set(0.3)
        self.max_depth_var.set(2)
        self.connection_threshold_var.set(0.8)
        self.enable_caching_var.set(True)
        self.parallel_processing_var.set(False)
        self.verbose_output_var.set(False)
        self.update_temperature_label()
        self.update_connection_label()

        if self.theme:
            # Show brief notification
            self.start_button.configure(text="⚡ Quick Preset Applied")
            self.parent.after(2000, lambda: self.start_button.configure(text="▶️ Start Analysis"))

    def apply_balanced_preset(self):
        """Apply balanced analysis preset"""
        self.temperature_var.set(0.7)
        self.max_depth_var.set(3)
        self.connection_threshold_var.set(0.7)
        self.enable_caching_var.set(True)
        self.parallel_processing_var.set(False)
        self.verbose_output_var.set(False)
        self.update_temperature_label()
        self.update_connection_label()

        if self.theme:
            self.start_button.configure(text="🎯 Balanced Preset Applied")
            self.parent.after(2000, lambda: self.start_button.configure(text="▶️ Start Analysis"))

    def apply_deep_preset(self):
        """Apply deep analysis preset"""
        self.temperature_var.set(1.0)
        self.max_depth_var.set(5)
        self.connection_threshold_var.set(0.5)
        self.enable_caching_var.set(True)
        self.parallel_processing_var.set(True)
        self.verbose_output_var.set(True)
        self.update_temperature_label()
        self.update_connection_label()

        if self.theme:
            self.start_button.configure(text="🔬 Deep Preset Applied")
            self.parent.after(2000, lambda: self.start_button.configure(text="▶️ Start Analysis"))
    
    def on_model_changed(self, event=None):
        """Handle model selection change"""
        self.update_model_info()
        self.update_model_status()
    
    def update_model_info(self):
        """Update model information display"""
        selected_model = self.model_var.get()
        
        # Find model info
        model_info = None
        for model in self.available_models:
            if model.get("name") == selected_model:
                model_info = model
                break
        
        if model_info:
            size = model_info.get("size", 0)
            size_mb = size / (1024 * 1024) if size else 0
            modified = model_info.get("modified_at", "Unknown")
            
            info_text = f"Size: {size_mb:.1f} MB"
            if modified != "Unknown":
                info_text += f" | Modified: {modified[:10]}"
            
            self.model_info_var.set(info_text)
        else:
            self.model_info_var.set("Model information not available")
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """Get current analysis configuration"""
        return {
            "type": self.analysis_type_var.get(),
            "model": self.model_var.get(),
            "temperature": self.temperature_var.get(),
            "max_depth": self.max_depth_var.get(),
            "connection_threshold": self.connection_threshold_var.get(),
            "enable_caching": self.enable_caching_var.get(),
            "parallel_processing": self.parallel_processing_var.get(),
            "verbose_output": getattr(self, 'verbose_output_var', tk.BooleanVar(value=False)).get(),
            "use_enhanced_analysis": getattr(self, 'enhanced_analysis_var', tk.BooleanVar(value=True)).get()
        }
    
    def start_analysis(self):
        """Start analysis with current configuration"""
        if not self.model_var.get():
            messagebox.showerror("No Model", "Please select an AI model first.")
            return
        
        config = self.get_analysis_config()
        self.start_callback(config)
    
    def reset(self):
        """Reset analysis parameters to defaults"""
        self.analysis_type_var.set("iterative")
        self.temperature_var.set(self.config.get("ollama.temperature", 0.7))
        self.max_depth_var.set(self.config.get("analysis.max_recursive_depth", 3))
        self.connection_threshold_var.set(self.config.get("analysis.connection_threshold", 0.7))
        self.enable_caching_var.set(self.config.get("analysis.enable_caching", True))
        self.parallel_processing_var.set(False)
        
        # Update UI
        self.on_analysis_type_changed()
        self.update_temperature_label()
        self.update_connection_label()
    
    def set_analysis_running(self, running: bool):
        """Update UI based on analysis running state"""
        state = "disabled" if running else "normal"
        
        self.start_button.configure(state=state)
        self.model_combo.configure(state="disabled" if running else "readonly")
        self.temperature_scale.configure(state=state)
        self.max_depth_spin.configure(state=state)
        self.connection_scale.configure(state=state)
        
        if running:
            self.start_button.configure(text="Analysis Running...")
        else:
            self.start_button.configure(text="Start Analysis")
    
    def cleanup(self):
        """Cleanup resources"""
        pass
