# Codebase Cleanup Plan

## Files to Remove

### 1. Redundant Documentation Files
- AI_Analysis_Program_Plan.md (outdated planning document)
- APPLICATION_LAUNCH_SUCCESS.md (temporary status file)
- Advanced_Analysis_Types_Plan.md (planning document)
- Button_Text_Cutoff_Fix.md (implementation notes)
- COMPREHENSIVE_TEST_SUITE_PLAN.md (planning document)
- ENHANCEMENTS.md (planning document)
- ETA_IMPLEMENTATION_SUMMARY.md (implementation notes)
- Enhanced_Analysis_Design.md (planning document)
- FINAL_SUMMARY.md (outdated summary)
- GUI_IMPROVEMENTS_INTEGRATION_GUIDE.md (implementation notes)
- GUI_Implementation_Summary.md (implementation notes)
- GUI_Improvements_Plan.md (planning document)
- GUI_Information_Fitting_Plan.md (planning document)
- GUI_LAYOUT_IMPROVEMENTS.md (implementation notes)
- GUI_Testing_Plan.md (planning document)
- IMPLEMENTATION_COMPLETE.md (status file)
- Interactive_Mind_Map_Plan.md (planning document)
- OLLAMA_API_IMPROVEMENT_PLAN.md (planning document)
- PERFORMANCE_IMPLEMENTATION_SUMMARY.md (implementation notes)
- Performance_Improvement_Plan.md (planning document)
- TASK_COMPLETION_SUMMARY.md (status file)
- USAGE_GUIDE.md (redundant with README.md)
- Visual_Performance_Improvement_Plan.md (planning document)

### 2. Redundant GUI Files
- gui/main_window.py (replaced by enhanced_main_window.py)
- gui/phase1_integration.py (temporary integration file)
- gui/phase1_integration_fixed.py (temporary integration file)
- gui/phase2_advanced_features.py (temporary integration file)
- gui/comprehensive_gui_improvements.py (temporary integration file)
- gui/advanced_content_management.py (redundant with content_management.py)
- gui/advanced_responsive_layout.py (redundant with responsive_layout.py)

### 3. Temporary/Test Files
- apply_gui_improvements.py (temporary integration script)
- demo_eta_gui.py (demo file)
- performance_test.py (test file)
- test_ollama_improvements.py (test file)
- test_topic_list.py (test file)
- validate_eta.py (validation file)
- validate_eta_100_confidence.py (validation file)

### 4. Cache and Build Files
- __pycache__ directories (all)
- desktop.ini (Windows system file)

## Files to Keep and Consolidate

### Core Application Files
- main.py (entry point)
- requirements.txt (dependencies)
- README.md (main documentation)
- config.json (configuration)

### Essential GUI Components
- gui/enhanced_main_window.py (main window)
- gui/topic_input.py (topic input)
- gui/topic_list.py (topic library)
- gui/analysis_panel.py (analysis controls)
- gui/results_viewer.py (results display)
- gui/settings_dialog.py (settings)
- gui/progress_dialog.py (progress tracking)
- gui/widgets.py (custom widgets)
- gui/responsive_layout.py (responsive layout)
- gui/enhanced_scrolling.py (scrolling improvements)
- gui/content_management.py (content management)
- gui/performance_upgrade.py (performance optimizations)
- gui/button_utils.py (button utilities)

### Analysis Components
- analysis/ollama_client.py (main client)
- analysis/enhanced_ollama_client.py (enhanced client)
- analysis/ollama_manager.py (connection management)
- analysis/advanced_analyzers.py (analysis types)
- analysis/mindmap_generator.py (mind map generation)
- analysis/context_framework.py (context handling)
- analysis/eta_predictor.py (time estimation)
- analysis/performance_optimizer.py (performance monitoring)

### Utilities and Data
- utils/ (all utility files)
- data/ (all data management files)
- templates/ (HTML templates)
- test_program.py (comprehensive test suite)

## Consolidation Actions

1. Update main.py to only import enhanced_main_window
2. Remove redundant imports from enhanced_main_window.py
3. Clean up unused performance and layout files
4. Consolidate button utilities into main components
5. Remove temporary integration files
